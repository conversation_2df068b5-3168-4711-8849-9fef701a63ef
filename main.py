# -*- coding: utf-8 -*-
import sys, time, os
import logging, ntwork
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import json
import keyboard
import pandas as pd

# 默认配置结构
DEFAULT_CONFIG = {
    "keywords": [
        {
            "keyword": "示例关键词",
            "match_mode": "fuzzy",  # fuzzy: 模糊匹配, exact: 精确匹配
            "reply_content": "这是示例回复内容"
        }
    ],
    "monitored_rooms": []  # 存储要监控的群ID列表
}


# 自定义日志处理器，将日志输出到GUI
class GuiHandler(logging.Handler):
    def __init__(self, text_widget):
        logging.Handler.__init__(self)
        self.text_widget = text_widget

    def emit(self, record):
        msg = self.format(record)

        def append():
            self.text_widget.configure(state="normal")
            self.text_widget.insert(tk.END, msg + "\n")
            self.text_widget.see(tk.END)
            self.text_widget.configure(state="disabled")

        self.text_widget.after(0, append)


class WeChatGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("企业微信关键词监控")
        self.root.geometry("1200x800")

        self.startup = None
        self.config = DEFAULT_CONFIG.copy()
        self.all_rooms = []  # 所有群列表

        self.create_widgets()
        # 设置日志
        self.setup_logging()
        self.load_config()

        # 自动初始化企业微信
        self.auto_init_wechat()

        

    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
        )
        self.logger = logging.getLogger()

        # 添加GUI处理器
        gui_handler = GuiHandler(self.log_text)
        gui_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
        )
        self.logger.addHandler(gui_handler)

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 创建左右分栏
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 左侧：关键词配置区
        self.create_keyword_config_frame(left_frame)

        # 右侧：群管理区
        self.create_room_management_frame(right_frame)

        # 底部：日志和控制区
        self.create_log_and_control_frame(main_frame)

        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

    def create_keyword_config_frame(self, parent):
        """创建关键词配置区域"""
        keywords_frame = ttk.LabelFrame(parent, text="关键词配置", padding="5")
        keywords_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 关键词列表
        list_frame = ttk.Frame(keywords_frame)
        list_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 创建Treeview来显示关键词配置
        columns = ('keyword', 'match_mode', 'reply', 'at_all')
        self.keyword_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)
        self.keyword_tree.heading('keyword', text='关键词')
        self.keyword_tree.heading('match_mode', text='匹配模式')
        self.keyword_tree.heading('reply', text='回复内容')
        self.keyword_tree.heading('at_all', text='@所有人')

        self.keyword_tree.column('keyword', width=120)
        self.keyword_tree.column('match_mode', width=80)
        self.keyword_tree.column('reply', width=200)

        # 添加滚动条
        keyword_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.keyword_tree.yview)
        self.keyword_tree.configure(yscrollcommand=keyword_scrollbar.set)

        self.keyword_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        keyword_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 输入区域
        input_frame = ttk.Frame(keywords_frame)
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(input_frame, text="关键词:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.keyword_entry = ttk.Entry(input_frame, width=20)
        self.keyword_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        ttk.Label(input_frame, text="匹配模式:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.match_mode_var = tk.StringVar(value="fuzzy")
        match_mode_combo = ttk.Combobox(input_frame, textvariable=self.match_mode_var,
                                       values=["fuzzy", "exact"], state="readonly", width=10)
        match_mode_combo.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))

        ttk.Label(input_frame, text="回复内容:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.reply_entry = ttk.Entry(input_frame, width=50)
        self.reply_entry.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        # 添加是否@所有人的选项
        self.at_all_var = tk.BooleanVar(value=False)
        at_all_check = ttk.Checkbutton(input_frame, text="@所有人", variable=self.at_all_var)
        at_all_check.grid(row=1, column=3, sticky=tk.W, padx=(10, 0))

        # 按钮区域
        btn_frame = ttk.Frame(keywords_frame)
        btn_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Button(btn_frame, text="添加关键词", command=self.add_keyword).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(btn_frame, text="修改选中", command=self.edit_keyword).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(btn_frame, text="删除选中", command=self.delete_keyword).grid(row=0, column=2, padx=(0, 5))
        ttk.Button(btn_frame, text="保存配置", command=self.save_config).grid(row=0, column=3, padx=(0, 5))

        # 绑定双击事件来编辑关键词
        self.keyword_tree.bind("<Double-1>", self.on_keyword_double_click)

        # 配置权重
        keywords_frame.columnconfigure(0, weight=1)
        keywords_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        input_frame.columnconfigure(1, weight=1)

    def create_room_management_frame(self, parent):
        """创建群管理区域"""
        room_frame = ttk.LabelFrame(parent, text="群管理", padding="5")
        room_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 所有群列表
        all_rooms_frame = ttk.LabelFrame(room_frame, text="所有群列表", padding="5")
        all_rooms_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        # 获取群列表按钮
        ttk.Button(all_rooms_frame, text="获取群列表", command=self.get_rooms_list).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # 所有群的Listbox
        all_rooms_list_frame = ttk.Frame(all_rooms_frame)
        all_rooms_list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.all_rooms_listbox = tk.Listbox(all_rooms_list_frame, selectmode=tk.EXTENDED, height=10)
        all_rooms_scrollbar = ttk.Scrollbar(all_rooms_list_frame, orient=tk.VERTICAL, command=self.all_rooms_listbox.yview)
        self.all_rooms_listbox.configure(yscrollcommand=all_rooms_scrollbar.set)

        self.all_rooms_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        all_rooms_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 选择按钮
        select_btn_frame = ttk.Frame(all_rooms_frame)
        select_btn_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(select_btn_frame, text="添加选中", command=self.add_selected_rooms).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(select_btn_frame, text="全选", command=self.select_all_rooms).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(select_btn_frame, text="清空选择", command=self.clear_room_selection).grid(row=0, column=2)

        # 监控群列表
        monitored_rooms_frame = ttk.LabelFrame(room_frame, text="监控群列表", padding="5")
        monitored_rooms_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 监控群的Listbox
        monitored_rooms_list_frame = ttk.Frame(monitored_rooms_frame)
        monitored_rooms_list_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.monitored_rooms_listbox = tk.Listbox(monitored_rooms_list_frame, selectmode=tk.EXTENDED, height=12)
        monitored_rooms_scrollbar = ttk.Scrollbar(monitored_rooms_list_frame, orient=tk.VERTICAL, command=self.monitored_rooms_listbox.yview)
        self.monitored_rooms_listbox.configure(yscrollcommand=monitored_rooms_scrollbar.set)

        self.monitored_rooms_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        monitored_rooms_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 移除按钮
        remove_btn_frame = ttk.Frame(monitored_rooms_frame)
        remove_btn_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))

        ttk.Button(remove_btn_frame, text="移除选中", command=self.remove_selected_rooms).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(remove_btn_frame, text="清空全部", command=self.clear_monitored_rooms).grid(row=0, column=1)

        # 配置权重
        room_frame.columnconfigure(0, weight=1)
        room_frame.columnconfigure(1, weight=1)
        room_frame.rowconfigure(0, weight=1)
        all_rooms_frame.columnconfigure(0, weight=1)
        all_rooms_frame.rowconfigure(1, weight=1)
        all_rooms_list_frame.columnconfigure(0, weight=1)
        all_rooms_list_frame.rowconfigure(0, weight=1)
        monitored_rooms_frame.columnconfigure(0, weight=1)
        monitored_rooms_frame.rowconfigure(0, weight=1)
        monitored_rooms_list_frame.columnconfigure(0, weight=1)
        monitored_rooms_list_frame.rowconfigure(0, weight=1)

    def create_log_and_control_frame(self, parent):
        """创建日志和控制区域"""
        # 日志显示区
        log_frame = ttk.LabelFrame(parent, text="运行日志", padding="5")
        log_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.log_text = scrolledtext.ScrolledText(log_frame, width=80, height=15)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.log_text.configure(state="disabled")

        # 控制按钮
        control_frame = ttk.Frame(parent, padding="5")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        self.start_btn = ttk.Button(
            control_frame, text="启动监控", command=self.start_monitoring
        )
        self.start_btn.grid(row=0, column=0, padx=(0, 5))

        self.stop_btn = ttk.Button(
            control_frame,
            text="停止监控",
            command=self.stop_monitoring,
            state="disabled",
        )
        self.stop_btn.grid(row=0, column=1, padx=(0, 5))

        ttk.Button(control_frame, text="加载配置", command=self.load_config).grid(row=0, column=2, padx=(0, 5))

        # 配置权重
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def auto_init_wechat(self):
        """自动初始化企业微信"""
        self.logger.info("正在自动初始化企业微信...")

        def init_thread():
            try:
                # 创建StartUp实例但不运行监控
                self.startup = StartUp(gui_instance=self, auto_run=False)

                # 在主线程中更新界面
                self.root.after(0, self.on_wechat_initialized)

            except Exception as e:
                self.logger.error(f"初始化企业微信失败: {e}")
                self.root.after(0, lambda: self.on_wechat_init_failed(str(e)))

        threading.Thread(target=init_thread, daemon=True).start()

    def on_wechat_initialized(self):
        """企业微信初始化完成后的回调"""
        self.logger.info("企业微信初始化完成，正在加载群列表...")
        self.all_rooms = self.startup.all_rooms
        self.update_all_rooms_display()
        self.logger.info("群列表加载完成")

    def on_wechat_init_failed(self, error_msg):
        """企业微信初始化失败后的回调"""
        messagebox.showerror("初始化失败", f"企业微信初始化失败：{error_msg}\n\n您可以稍后手动点击'获取群列表'重试。")

    def load_config(self):
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                self.config = json.load(f)
                self.logger.info("配置文件加载成功")
        except FileNotFoundError:
            self.logger.info("配置文件不存在，使用默认配置")
            self.config = DEFAULT_CONFIG.copy()
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = DEFAULT_CONFIG.copy()

        # 更新界面显示
        self.update_keyword_display()
        self.update_monitored_rooms_display()

    def save_config(self):
        """保存配置文件"""
        try:
            with open("config.json", "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            self.logger.info("配置保存成功")
            messagebox.showinfo("成功", "配置保存成功！")
        except Exception as e:
            self.logger.error(f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")

    def update_keyword_display(self):
        """更新关键词显示"""
        # 清空现有显示
        for item in self.keyword_tree.get_children():
            self.keyword_tree.delete(item)

        # 添加配置中的关键词
        for keyword_config in self.config.get("keywords", []):
            match_mode_text = "模糊匹配" if keyword_config["match_mode"] == "fuzzy" else "精确匹配"
            at_all_text = "是" if keyword_config.get("at_all", False) else "否"
            self.keyword_tree.insert("", tk.END, values=(
                keyword_config["keyword"],
                match_mode_text,
                keyword_config["reply_content"],
                at_all_text
            ))

    def update_monitored_rooms_display(self):
        """更新监控群显示"""
        self.monitored_rooms_listbox.delete(0, tk.END)
        for room_id in self.config.get("monitored_rooms", []):
            # 查找群名称
            room_name = self.get_room_name_by_id(room_id)
            display_text = f"{room_name} ({room_id})" if room_name else room_id
            self.monitored_rooms_listbox.insert(tk.END, display_text)

    def get_room_name_by_id(self, room_id):
        """根据群ID获取群名称"""
        for room in self.all_rooms:
            if room.get("conversation_id") == room_id:
                return room.get("nickname", "未知群名")
        return None

    def add_keyword(self):
        """添加关键词"""
        keyword = self.keyword_entry.get().strip()
        match_mode = self.match_mode_var.get()
        reply_content = self.reply_entry.get().strip()

        if not keyword:
            messagebox.showwarning("警告", "请输入关键词")
            return

        if not reply_content:
            messagebox.showwarning("警告", "请输入回复内容")
            return

        # 检查是否已存在相同关键词
        for existing in self.config["keywords"]:
            if existing["keyword"] == keyword:
                messagebox.showwarning("警告", "该关键词已存在")
                return

        # 添加到配置
        new_keyword = {
            "keyword": keyword,
            "match_mode": match_mode,
            "reply_content": reply_content,
            "at_all": self.at_all_var.get()
        }
        self.config["keywords"].append(new_keyword)

        # 更新显示
        self.update_keyword_display()

        # 清空输入框
        self.keyword_entry.delete(0, tk.END)
        self.reply_entry.delete(0, tk.END)
        self.match_mode_var.set("fuzzy")

        self.logger.info(f"添加关键词: {keyword}")

    def delete_keyword(self):
        """删除选中的关键词"""
        selected_items = self.keyword_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请选择要删除的关键词")
            return

        if messagebox.askyesno("确认", "确定要删除选中的关键词吗？"):
            # 获取选中项的关键词
            for item in selected_items:
                values = self.keyword_tree.item(item, "values")
                keyword = values[0]

                # 从配置中删除
                self.config["keywords"] = [
                    kw for kw in self.config["keywords"]
                    if kw["keyword"] != keyword
                ]

            # 更新显示
            self.update_keyword_display()
            self.logger.info("删除关键词成功")

    def edit_keyword(self):
        """编辑选中的关键词"""
        selected_items = self.keyword_tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请选择要修改的关键词")
            return

        if len(selected_items) > 1:
            messagebox.showwarning("警告", "请只选择一个关键词进行修改")
            return

        # 获取选中项的信息
        item = selected_items[0]
        values = self.keyword_tree.item(item, "values")
        keyword = values[0]
        match_mode_text = values[1]
        reply_content = values[2]

        # 转换匹配模式文本为值
        match_mode = "fuzzy" if match_mode_text == "模糊匹配" else "exact"
        # 设置是否@所有人
        self.at_all_var.set(values[3] == "是")

        # 填充到输入框
        self.keyword_entry.delete(0, tk.END)
        self.keyword_entry.insert(0, keyword)
        self.match_mode_var.set(match_mode)
        self.reply_entry.delete(0, tk.END)
        self.reply_entry.insert(0, reply_content)

        # 从配置中删除原有的关键词
        self.config["keywords"] = [
            kw for kw in self.config["keywords"]
            if kw["keyword"] != keyword
        ]

        # 更新显示
        self.update_keyword_display()

        self.logger.info(f"正在编辑关键词: {keyword}")

    def on_keyword_double_click(self, event):
        """双击关键词列表项时的处理"""
        # 确保点击的是有效项
        if self.keyword_tree.selection():
            self.edit_keyword()

    def get_rooms_list(self):
        """获取群列表"""
        if not self.startup or not hasattr(self.startup, 'wechat'):
            # 如果没有startup实例，尝试自动初始化
            self.auto_init_wechat()
            return

        def get_rooms_thread():
            try:
                self.logger.info("正在获取群列表...")
                self.startup.get_all_rooms()
                self.all_rooms = self.startup.all_rooms

                # 在主线程中更新界面
                self.root.after(0, self.update_all_rooms_display)

            except Exception as e:
                self.logger.error(f"获取群列表失败: {e}")

        threading.Thread(target=get_rooms_thread, daemon=True).start()

    def update_all_rooms_display(self):
        """更新所有群列表显示"""
        self.all_rooms_listbox.delete(0, tk.END)
        for room in self.all_rooms:
            room_name = room.get("nickname", "未知群名")
            room_id = room.get("conversation_id", "")
            display_text = f"{room_name} ({room_id})"
            self.all_rooms_listbox.insert(tk.END, display_text)

        self.logger.info(f"群列表更新完成，共 {len(self.all_rooms)} 个群")

    def add_selected_rooms(self):
        """添加选中的群到监控列表"""
        selected_indices = self.all_rooms_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要添加的群")
            return

        added_count = 0
        for index in selected_indices:
            if index < len(self.all_rooms):
                room = self.all_rooms[index]
                room_id = room.get("conversation_id")

                # 检查是否已在监控列表中
                if room_id not in self.config["monitored_rooms"]:
                    self.config["monitored_rooms"].append(room_id)
                    added_count += 1

        if added_count > 0:
            self.update_monitored_rooms_display()
            self.logger.info(f"添加了 {added_count} 个群到监控列表")
        else:
            messagebox.showinfo("提示", "选中的群已在监控列表中")

    def select_all_rooms(self):
        """全选所有群"""
        self.all_rooms_listbox.select_set(0, tk.END)

    def clear_room_selection(self):
        """清空群选择"""
        self.all_rooms_listbox.selection_clear(0, tk.END)

    def remove_selected_rooms(self):
        """从监控列表中移除选中的群"""
        selected_indices = self.monitored_rooms_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("警告", "请选择要移除的群")
            return

        if messagebox.askyesno("确认", "确定要移除选中的群吗？"):
            # 从后往前删除，避免索引变化
            for index in reversed(selected_indices):
                if index < len(self.config["monitored_rooms"]):
                    del self.config["monitored_rooms"][index]

            self.update_monitored_rooms_display()
            self.logger.info("移除群成功")

    def clear_monitored_rooms(self):
        """清空所有监控群"""
        if messagebox.askyesno("确认", "确定要清空所有监控群吗？"):
            self.config["monitored_rooms"] = []
            self.update_monitored_rooms_display()
            self.logger.info("清空监控群列表")

    def start_monitoring(self):
        # 检查是否有监控群
        if not self.config.get("monitored_rooms"):
            messagebox.showwarning("警告", "请先选择要监控的群")
            return

        # 检查是否有关键词配置
        if not self.config.get("keywords"):
            messagebox.showwarning("警告", "请先配置关键词")
            return

        self.start_btn.configure(state="disabled")
        self.stop_btn.configure(state="normal")

        def run():
            try:
                # 如果已经有startup实例（自动初始化创建的），直接使用
                if self.startup and hasattr(self.startup, 'wechat'):
                    self.startup.auto_run = True
                    self.startup.run()
                else:
                    # 否则创建新的实例
                    self.startup = StartUp(gui_instance=self)
                    self.startup.run()
            except Exception as e:
                self.logger.error(f"监控发生错误: {e}")
                self.root.after(0, self.stop_monitoring)

        self.monitor_thread = threading.Thread(target=run, daemon=True)
        self.monitor_thread.start()
        self.logger.info("监控已启动")

    def stop_monitoring(self):
        if self.startup:
            self.startup.exit_program()
            self.startup = None

        self.start_btn.configure(state="normal")
        self.stop_btn.configure(state="disabled")
        self.logger.info("监控已停止")

    def match_keyword(self, message_content):
        """匹配关键词并返回回复内容"""
        for keyword_config in self.config.get("keywords", []):
            keyword = keyword_config["keyword"]
            match_mode = keyword_config["match_mode"]
            reply_content = keyword_config["reply_content"]

            if match_mode == "exact":
                # 精确匹配
                if keyword == message_content.strip():
                    return reply_content
            else:
                # 模糊匹配（默认）
                if keyword in message_content:
                    return reply_content

        return None

    def run(self):
        self.root.mainloop()


# 修改StartUp类
class StartUp:
    def __init__(self, smart=True, gui_instance=None, auto_run=True):
        # 添加日志记录器
        self.logger = logging.getLogger()
        self.gui_instance = gui_instance
        self.auto_run = auto_run

        self.wechat = ntwork.WeWork()
        self.exit_flag = False

        # 初始化配置
        self.init_config()

        # 初始化微信
        self.init_wechat(smart)

        # 注册消息回调
        self.wechat.msg_register(ntwork.MT_RECV_TEXT_MSG)(self.on_recv_message)

    def init_config(self):
        # 从GUI获取配置
        if self.gui_instance:
            self.config = self.gui_instance.config
            self.logger.info(f"从GUI加载配置，关键词数量: {len(self.config.get('keywords', []))}, 监控群数量: {len(self.config.get('monitored_rooms', []))}")
        else:
            # 如果没有GUI实例，尝试直接读取配置文件
            try:
                with open("config.json", "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            except Exception as e:
                self.logger.error(f"读取配置文件失败: {e}")
                self.config = DEFAULT_CONFIG.copy()

     

    def init_wechat(self, smart):
        self.wechat.open(smart=smart)
        self.logger.info("等待登录......")
        self.wechat.wait_login()
        login_info = self.wechat.get_login_info()
        self.user_id = login_info["user_id"]
        self.name = login_info["nickname"]
        self.logger.info(f"登录信息:>>>user_id:{self.user_id}>>>>>>>>name:{self.name}")

        self.logger.info("静默延迟60s，等待客户端刷新数据，请勿进行任何操作......")
        time.sleep(60)

        self.save_contact_info()
        self.get_all_rooms()
        self.setup_all_rooms_monitoring()

    def save_contact_info(self):
        try:
            self.contacts = self.wechat.get_external_contacts()
            self.rooms = self.wechat.get_rooms()

            if not self.contacts or not self.rooms:
                self.logger.error("获取contacts或rooms失败，程序退出")
                self.exit_program()
                return

            directory = os.path.join(os.getcwd(), "tmp")
            if not os.path.exists(directory):
                os.makedirs(directory)

            # 保存联系人信息
            self.save_json_file(
                os.path.join(directory, "wework_contacts.json"), self.contacts
            )
            self.save_json_file(
                os.path.join(directory, "wework_rooms.json"), self.rooms
            )

            # 保存群成员信息
            room_members = {}
            for room in self.rooms["room_list"]:
                room_wxid = room["conversation_id"]
                room_members[room_wxid] = self.wechat.get_room_members(room_wxid)

            self.save_json_file(
                os.path.join(directory, "wework_room_members.json"), room_members
            )
            self.logger.info("wework程序初始化完成")
        except Exception as e:
            self.logger.error(f"保存联系人信息失败: {e}")
            self.exit_program()

    def save_json_file(self, filepath, data):
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)

    def get_all_rooms(self):
        """获取所有群列表，参考cankao.py中的refresh_room_list方法"""
        try:
            self.logger.info("正在获取所有群列表...")

            # 添加重试机制
            max_retries = 3
            self.all_rooms = []
            total_rooms = 0
            current_page = 1
            room_page_size = 50  # 每页获取的群数量

            while True:
                success = False
                for i in range(max_retries):
                    try:
                        # 带分页参数获取群列表
                        rooms = self.wechat.get_rooms(page_num=current_page, page_size=room_page_size)
                        if rooms and 'room_list' in rooms and rooms['room_list']:
                            room_count = len(rooms['room_list'])
                            self.logger.info(f"成功获取第 {current_page} 页群列表，有 {room_count} 个群")
                            self.all_rooms.extend(rooms['room_list'])
                            total_rooms += room_count
                            success = True
                            break
                        else:
                            # 如果返回空列表，可能已经获取完所有群
                            if current_page > 1:
                                self.logger.info(f"第 {current_page} 页没有更多群，获取完成")
                                success = True
                                break
                            else:
                                self.logger.warning(f"第 {current_page} 页获取群列表为空，尝试重试... ({i + 1}/{max_retries})")
                                time.sleep(2)
                    except Exception as e:
                        self.logger.warning(f"获取群列表第 {current_page} 页失败: {e}，尝试重试... ({i + 1}/{max_retries})")
                        time.sleep(2)

                # 如果当前页获取失败且是第一页，则无法继续
                if not success and current_page == 1:
                    self.logger.error("无法获取群列表，请检查企业微信状态")
                    return

                # 如果当前页获取的群数量少于页大小，说明已经没有更多群了
                if success and (len(rooms.get('room_list', [])) < room_page_size):
                    self.logger.info(f"已获取所有群，总共 {total_rooms} 个群")
                    break

                # 继续获取下一页
                current_page += 1

            self.logger.info(f"成功获取所有群列表，共 {len(self.all_rooms)} 个群")

        except Exception as e:
            self.logger.error(f"获取所有群列表失败: {str(e)}")
            self.all_rooms = []

    def on_recv_message(self, wechat_instance: ntwork.WeWork, message):
        start = time.time()
        data = message["data"]
        query = data.get("content", "").strip()
        conversation_id = message["data"].get("conversation_id")
        from_wxid = message["data"].get("sender")
        sender_name = message["data"].get("sender_name")
        my_wxid = wechat_instance.get_login_info()["user_id"]

        is_group = True if conversation_id.startswith("R:") else False

        # 只处理群聊消息，且不是自己发送的消息
        if from_wxid == my_wxid or not is_group or not query:
            return

        # 检查是否是监控的群
        monitored_rooms = self.config.get("monitored_rooms", [])
        if conversation_id not in monitored_rooms:
            return

        self.logger.info(f"收到监控群 {conversation_id} 中 {sender_name} 的消息: {query}")

        # 使用GUI的关键词匹配逻辑
        if self.gui_instance:
            reply_content = self.gui_instance.match_keyword(query)
            if reply_content:
                self.logger.info(f"匹配到关键词，回复: {reply_content}")
                try:
                    # 检查是否@所有人
                    keyword_config = next((kw for kw in self.gui_instance.config["keywords"] 
                                         if kw["keyword"] in query), None)
                    print(keyword_config)
                    if keyword_config and keyword_config.get("at_all", False):
                        wechat_instance.send_room_at_msg(
                            conversation_id=conversation_id,
                            content=reply_content,
                            at_list=["0"]
                        )
                    else:
                        wechat_instance.send_text(
                            conversation_id=conversation_id,
                            content=reply_content
                        )
                    end_time = time.time()
                    self.logger.info(f"回复发送成功，耗时: {end_time - start:.2f}秒")
                except Exception as e:
                    self.logger.error(f"发送回复失败: {e}")
                return

        
    def setup_all_rooms_monitoring(self):
        """设置监控选定的群"""
        monitored_rooms = self.config.get("monitored_rooms", [])
        if monitored_rooms:
            self.logger.info(f"设置监控选定的群，共 {len(monitored_rooms)} 个群")

            # 打印监控群的信息
            for room_id in monitored_rooms:
                room_name = "未知群名"
                if hasattr(self, 'all_rooms') and self.all_rooms:
                    for room in self.all_rooms:
                        if room.get('conversation_id') == room_id:
                            room_name = room.get('nickname', '未知群名')
                            break
                self.logger.info(f"监控群: {room_name} - {room_id}")
        else:
            self.logger.warning("未配置监控群，将不会处理任何群消息")

    def exit_program(self):
        self.logger.info("正在退出程序...")
        self.exit_flag = True
        keyboard.unhook_all()
        ntwork.exit_()
        sys.exit(0)

    def run(self):
        # 如果不是自动运行模式，直接返回
        if not self.auto_run:
            return

        def exit_on_ctrl_q(event):
            if (
                event.event_type == "down"
                and event.name == "q"
                and keyboard.is_pressed("ctrl")
            ):
                self.exit_program()

        keyboard.on_press(exit_on_ctrl_q)
        try:
            while not self.exit_flag:
                time.sleep(1)
        except KeyboardInterrupt:
            self.exit_program()


if __name__ == "__main__":
    gui = WeChatGUI()
    gui.run()
