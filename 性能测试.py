# -*- coding: utf-8 -*-
"""
性能测试脚本
用于测试关键词匹配性能优化效果
"""
import time
import random
import string

# 模拟WeChatGUI的关键词匹配功能
class MockWeChatGUI:
    def __init__(self):
        # 模拟配置
        self.config = {
            "keywords": [
                {"keyword": "你好", "match_mode": "exact", "reply_content": "您好！", "at_all": False},
                {"keyword": "帮助", "match_mode": "exact", "reply_content": "我来帮您", "at_all": False},
                {"keyword": "测试", "match_mode": "fuzzy", "reply_content": "这是测试回复", "at_all": False},
                {"keyword": "问题", "match_mode": "fuzzy", "reply_content": "请描述您的问题", "at_all": False},
                {"keyword": "谢谢", "match_mode": "exact", "reply_content": "不客气！", "at_all": False},
            ]
        }
        
        # 添加更多关键词进行压力测试
        for i in range(100):
            self.config["keywords"].append({
                "keyword": f"关键词{i}",
                "match_mode": "exact" if i % 2 == 0 else "fuzzy",
                "reply_content": f"回复{i}",
                "at_all": False
            })
        
        # 初始化缓存
        self.keyword_cache = {}
        self.exact_keywords = set()
        self.fuzzy_keywords = []
        self.rebuild_keyword_cache()
    
    def rebuild_keyword_cache(self):
        """重建关键词缓存"""
        self.keyword_cache.clear()
        self.exact_keywords.clear()
        self.fuzzy_keywords.clear()
        
        for keyword_config in self.config.get("keywords", []):
            keyword = keyword_config["keyword"]
            match_mode = keyword_config["match_mode"]
            
            if match_mode == "exact":
                self.exact_keywords.add(keyword)
            else:
                self.fuzzy_keywords.append(keyword_config)
            
            self.keyword_cache[keyword] = keyword_config
    
    def match_keyword_old(self, message_content):
        """旧版本的关键词匹配方法（用于对比）"""
        for keyword_config in self.config.get("keywords", []):
            keyword = keyword_config["keyword"]
            match_mode = keyword_config["match_mode"]
            reply_content = keyword_config["reply_content"]

            if match_mode == "exact":
                if keyword == message_content.strip():
                    return reply_content, keyword_config
            else:
                if keyword in message_content:
                    return reply_content, keyword_config
        return None, None
    
    def match_keyword_new(self, message_content):
        """优化后的关键词匹配方法"""
        message_stripped = message_content.strip()
        
        # 首先检查精确匹配
        if message_stripped in self.exact_keywords:
            keyword_config = self.keyword_cache[message_stripped]
            return keyword_config["reply_content"], keyword_config
        
        # 然后检查模糊匹配
        for keyword_config in self.fuzzy_keywords:
            keyword = keyword_config["keyword"]
            if keyword in message_content:
                return keyword_config["reply_content"], keyword_config
        
        return None, None

def generate_test_messages(count=1000):
    """生成测试消息"""
    messages = []
    
    # 添加一些会匹配的消息
    match_messages = ["你好", "帮助", "谢谢", "这是测试消息", "我有问题"]
    for _ in range(count // 2):
        messages.append(random.choice(match_messages))
    
    # 添加一些不会匹配的随机消息
    for _ in range(count // 2):
        length = random.randint(5, 20)
        message = ''.join(random.choices(string.ascii_letters + string.digits + '，。！？', k=length))
        messages.append(message)
    
    return messages

def performance_test():
    """性能测试"""
    print("开始性能测试...")
    
    gui = MockWeChatGUI()
    test_messages = generate_test_messages(1000)
    
    print(f"测试消息数量: {len(test_messages)}")
    print(f"关键词数量: {len(gui.config['keywords'])}")
    print(f"精确匹配关键词: {len(gui.exact_keywords)}")
    print(f"模糊匹配关键词: {len(gui.fuzzy_keywords)}")
    print("-" * 50)
    
    # 测试旧版本方法
    print("测试旧版本匹配方法...")
    start_time = time.time()
    old_matches = 0
    for message in test_messages:
        reply, config = gui.match_keyword_old(message)
        if reply:
            old_matches += 1
    old_time = time.time() - start_time
    
    # 测试新版本方法
    print("测试新版本匹配方法...")
    start_time = time.time()
    new_matches = 0
    for message in test_messages:
        reply, config = gui.match_keyword_new(message)
        if reply:
            new_matches += 1
    new_time = time.time() - start_time
    
    # 输出结果
    print("\n" + "=" * 50)
    print("性能测试结果:")
    print("=" * 50)
    print(f"旧版本方法:")
    print(f"  总耗时: {old_time:.4f}秒")
    print(f"  平均每条消息: {old_time/len(test_messages)*1000:.3f}毫秒")
    print(f"  匹配数量: {old_matches}")
    
    print(f"\n新版本方法:")
    print(f"  总耗时: {new_time:.4f}秒")
    print(f"  平均每条消息: {new_time/len(test_messages)*1000:.3f}毫秒")
    print(f"  匹配数量: {new_matches}")
    
    if old_time > 0:
        improvement = (old_time - new_time) / old_time * 100
        speedup = old_time / new_time if new_time > 0 else float('inf')
        print(f"\n性能提升:")
        print(f"  提升幅度: {improvement:.1f}%")
        print(f"  速度倍数: {speedup:.1f}x")
    
    # 验证结果一致性
    if old_matches == new_matches:
        print(f"\n✓ 结果验证: 匹配结果一致 ({old_matches} 条)")
    else:
        print(f"\n✗ 结果验证: 匹配结果不一致 (旧:{old_matches}, 新:{new_matches})")

if __name__ == "__main__":
    performance_test()
