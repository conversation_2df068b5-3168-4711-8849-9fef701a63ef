# 自动回复开关功能使用说明

## 功能概述

新增的自动回复开关功能允许您通过GUI界面或私聊指令来控制自动回复的开启和关闭状态。

## 主要特性

### 1. GUI控制
- 在主界面底部控制区域新增了"自动回复"复选框
- 可以直接点击复选框来开启/关闭自动回复
- 状态会实时保存到配置文件中

### 2. 私聊指令控制
- 支持通过给自己发私聊消息来远程控制
- 无需打开GUI界面即可控制自动回复状态
- 支持多种实用指令

### 3. 状态持久化
- 自动回复开关状态会保存到config.json文件中
- 程序重启后会自动恢复上次的开关状态
- 默认状态为开启

## 使用方法

### GUI界面控制

1. **开启自动回复**：
   - 勾选主界面底部的"自动回复"复选框
   - 系统会显示"自动回复已开启"的日志信息

2. **关闭自动回复**：
   - 取消勾选"自动回复"复选框
   - 系统会显示"自动回复已关闭"的日志信息

### 私聊指令控制

在企业微信中给自己发私聊消息，支持以下指令：

#### 基本控制指令

| 指令 | 功能 | 回复消息 |
|------|------|----------|
| `开启` | 开启自动回复 | ✅ 自动回复已开启 |
| `关闭` | 关闭自动回复 | ❌ 自动回复已关闭 |
| `状态` | 查看当前状态和性能统计 | 🤖 自动回复状态: 开启/关闭<br>📊 性能统计信息 |

#### 帮助指令

| 指令 | 功能 |
|------|------|
| `帮助` | 显示完整的指令帮助信息 |
| `help` | 显示完整的指令帮助信息 |
| `?` | 显示完整的指令帮助信息 |

### 使用示例

1. **开启自动回复**：
   ```
   给自己发消息：开启
   系统回复：✅ 自动回复已开启
   ```

2. **关闭自动回复**：
   ```
   给自己发消息：关闭
   系统回复：❌ 自动回复已关闭
   ```

3. **查看状态**：
   ```
   给自己发消息：状态
   系统回复：🤖 自动回复状态: 开启
   📊 消息统计: 总计 150 条, 匹配 45 条 (30.0%)
   响应时间: 平均 0.025s, 最快 0.015s, 最慢 0.045s
   ```

4. **获取帮助**：
   ```
   给自己发消息：帮助
   系统回复：🤖 自动回复控制指令：
   
   📝 可用指令：
   • 开启 - 开启自动回复
   • 关闭 - 关闭自动回复  
   • 状态 - 查看当前状态和统计
   • 帮助 - 显示此帮助信息
   
   💡 使用方法：
   在私聊中发送指令即可控制自动回复功能
   ```

## 工作原理

### 消息处理逻辑

1. **私聊指令检测**：
   - 系统会检测是否为私聊消息（not is_group）
   - 检查发送者是否为自己（from_wxid == my_wxid）
   - 如果满足条件，则进入指令处理流程

2. **群聊消息处理**：
   - 在处理群聊消息前，会检查自动回复开关状态
   - 如果开关关闭，则跳过所有关键词匹配和回复逻辑
   - 如果开关开启，则正常处理消息

3. **状态同步**：
   - GUI界面和私聊指令控制的状态会实时同步
   - 任何一种方式的状态变更都会立即生效

### 配置文件结构

config.json文件中新增了auto_reply_switch字段：

```json
{
    "keywords": [...],
    "monitored_rooms": [...],
    "auto_reply_switch": true
}
```

## 注意事项

1. **私聊指令限制**：
   - 只有给自己发送的私聊消息才会被识别为指令
   - 其他人发送的消息不会触发指令处理

2. **指令格式**：
   - 指令需要完全匹配，区分大小写
   - 建议使用中文指令以避免输入错误

3. **状态持久化**：
   - 每次状态变更都会自动保存到配置文件
   - 如果保存失败，会在日志中显示错误信息

4. **性能影响**：
   - 关闭自动回复后，系统仍会接收消息但不会进行关键词匹配
   - 这可以显著减少系统资源消耗

## 故障排除

### 常见问题

1. **指令无响应**：
   - 确认是在私聊中给自己发送消息
   - 检查指令拼写是否正确
   - 查看程序日志是否有错误信息

2. **状态不同步**：
   - 重启程序后检查状态是否正确加载
   - 检查config.json文件是否存在写入权限问题

3. **GUI复选框状态错误**：
   - 尝试手动点击复选框重新同步状态
   - 重新加载配置文件

### 日志信息

系统会记录以下相关日志：

- `自动回复已开启/关闭`：状态变更日志
- `通过私聊指令开启/关闭自动回复`：私聊指令操作日志
- `自动回复已关闭，跳过消息处理`：消息跳过日志
- `自动回复开关状态已保存/加载`：配置操作日志

通过查看这些日志可以了解系统的运行状态和排查问题。

## 更新内容

本次更新新增的功能：

1. ✅ 自动回复开关变量和GUI控件
2. ✅ 私聊指令处理逻辑
3. ✅ 状态持久化机制
4. ✅ 性能统计中包含开关状态
5. ✅ 完整的指令帮助系统
6. ✅ 实时状态同步机制

这些功能让您可以更灵活地控制自动回复系统，提升使用体验。
